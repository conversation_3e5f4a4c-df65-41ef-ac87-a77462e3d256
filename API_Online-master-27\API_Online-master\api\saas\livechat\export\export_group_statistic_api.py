# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function： 技能组统计-导出

from urllib.parse import urlencode

from api.saas.online_base_saas_api import OnlineBaseSaasApi

'''技能组统计-导出'''
class ExportGroupStatisticApi(OnlineBaseSaasApi):
    def __init__(self, taskType,startDate, endDate, description,allViewFlag, departIdsParam, groupIdsParam):
        super().__init__()
        self.url = self.host + "/chat-statistics/export/task"
        self.method = "post"
        self.headers["Content-Type"] = "application/json"
        self.json = {
            "taskType": taskType,
            "allViewFlag": allViewFlag,
            "departIdsParam": departIdsParam,
            "startDate": startDate,
            "endDate": endDate,
            "description": description,
            "groupIdsParam":groupIdsParam
        }

