# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function： 会话统计-导出会话数据

from urllib.parse import urlencode

from api.saas.online_base_saas_api import OnlineBaseSaasApi

'''会话统计-导出会话数据'''
class ExportChatStatisticDataApi(OnlineBaseSaasApi):
    def __init__(self,taskType,source,description,searchWay,startDate,endDate,channelFlag,channelName,timeZoneInReqHeader):
        super().__init__()
        self.url = self.host + "/chat-statistics/export/task"
        self.method = "post"
        self.headers["Content-Type"] = "application/json"
        self.json = {
            "taskType":taskType,
            "startDate":startDate,
            "endDate":endDate,
            "searchWay":searchWay,
            "source":source,
            "channelFlag":channelFlag,
            "channelName":channelName,
            "timeZoneInReqHeader":timeZoneInReqHeader,
            "description":description
        }

