# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function： 满意度评价统计-会话满意度统计-导出

from urllib.parse import urlencode

from api.saas.online_base_saas_api import OnlineBaseSaasApi

'''满意度评价统计-会话满意度统计-导出'''
class ExportSatisfactionStatisticApi(OnlineBaseSaasApi):
    def __init__(self,taskType,source,description,searchWay,startDate,endDate,departIdsParam,allViewFlag,
                 groupIdsParam,staffIdsParam,channelFlag,timeZoneInReqHeader,compareTime):
        super().__init__()
        self.url = self.host + "/chat-statistics/export/task"
        self.method = "post"
        self.headers["Content-Type"] = "application/json"
        self.json = {
            "taskType": taskType,
            "searchWay": searchWay,
            "startDate": startDate,
            "endDate": endDate,
            "departIdsParam": departIdsParam,
            "allViewFlag": allViewFlag,
            "groupIdsParam": groupIdsParam,
            "staffIdsParam": staffIdsParam,
            "source": source,
            "channelFlag": channelFlag,
            "timeZoneInReqHeader": timeZoneInReqHeader,
            "compareTime": compareTime,
            "description": description,
        }

