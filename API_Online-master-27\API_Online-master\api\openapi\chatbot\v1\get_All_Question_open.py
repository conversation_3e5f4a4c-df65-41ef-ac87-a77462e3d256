# -*- coding: UTF-8 -*-
from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class GetAllQuestionApiOpen(OnlineBaseOpen_v1_Api):
    """
    获取知识库分类
    """
    def __init__(self, robotFlag, typeFlag):
        super().__init__()
        self.url =self.host + '/kb-service/getAllQuestion/4'
        self.method = 'get'
        self.headers['content-type'] = "application/json"


        self.params = {
            "robotFlag": robotFlag,   # 知识库所属机器人：0-公共知识库，1-机器人
            "typeFlag": typeFlag      # 默认1 单轮  2  多轮
        }
