import json
import random

from api.saas.chatbot.v1.Knowledge_Base.One_round_Chat.get_OnLine_Robot_List_api import GetOnLineRobotListApi
# from api.saas.v6.v6_Knowledge_Base.Multi_round_Chat.add_Robot_Doc_Info_api import AddRobotDocInfo<PERSON>pi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.branch_Link_Node_api import BranchLinkNodeApi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.create_Branch_api import CreateBranchApi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.create_Node_1_api import CreateNode<PERSON>ne<PERSON><PERSON>
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.create_Node_api import CreateNodeApi
# from api.saas.v6.v6_Knowledge_Base.Multi_round_Chat.query_Process_List_api import QueryProcess<PERSON>ist<PERSON>pi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.query_Variables_Page_By_Duolun_api import \
    QueryVariablesPageByDuolunApi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.release_Process_Info_api import ReleaseProcessInfoApi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.save_Process_Info_api import SaveProcessInfoApi
from api.saas.chatbot.v6.v6_Knowledge_Base.Multi_round_Chat.update_First_Node_api import UpdateFirstNodeApi
from api.saas.chatbot.v6.v6_Knowledge_Base.One_round_Chat.robot_v6_api import GetQueryRobotsApi, AddRobotDocApi, \
    GetKbListApi, \
    UserInitV6Api, SearchDocListApi, v2_actionApi, out_actionApi, GetKbListMultiApi, GetAllKbTypeListApi, \
    MultiSearchDocListApi, QueryProcessListApi, AddRobotDocInfoApi, CommentActionApi, DeleteDocApi, DeleteMultiDocApi, \
    DeleteProcessApi
from data.Saas.chatbot.v6.robot_v6qa_data import add_robot_doc, init_v6_data, search_doc_list_data, v2_action_data, \
    out_action_data, get_query_process_list_data, add_robot_doc_multi_data, get_multi_search_doc_list_data, \
    comment_action_data, delete_doc_data, delete_multi_doc_data, delete_multi_process_data
from zc_apitest_common.get_keyword import GetKeyword


# region 获取机器人列表
def get_query_robots():
    resp = GetQueryRobotsApi().send_request().json()
    items = resp['items']
    return items


# endregion

# region 获取机器人单轮分类列表
def get_kb_list(robot_id=None):
    if robot_id is None:
        robot_id = get_query_robots()[0]['robotFlag']
    resp = GetKbListApi(robot_id).send_request().json()
    items = resp['items']
    return items


# endregion

# region 获取机器人多轮分类列表
def get_multi_kb_list():
    robotIds = []
    robot_id = get_query_robots()[0]['robotFlag']
    robotIds.append(robot_id)
    resp = GetKbListMultiApi(robotIds).send_request().json()
    items = resp['items']
    return items


# endregion


# region 获取多轮知识库及其分类信息
def get_multi_kb_info():
    robotIds = []
    robot_id = get_query_robots()[0]['robotFlag']
    robotIds.append(robot_id)
    robotId = robot_id
    typeFlag = 2
    resp = GetAllKbTypeListApi(robotId, typeFlag, robotIds).send_request().json()
    items = resp['items']
    return items


# endregion


# region 获取多轮问法管理列表
def get_multi_search_doc_list_action(keyWordsArr=None, usedType=None, libId=None, questionTypeId=None):
    params = get_multi_search_doc_list_data(keyWordsArr, usedType, libId, questionTypeId)
    resp = MultiSearchDocListApi(**params).send_request().json()
    items = resp['items']
    return items


# endregion

# region 获取机器人多轮流程列表
def get_query_process_list_action(robotFlag, keyWordsArr=None):
    params = get_query_process_list_data(robotFlag, keyWordsArr)
    print(params)
    resp = QueryProcessListApi(**params).send_request().json()
    items = resp['items']
    return items


# endregion


# region 新增单轮问题
def add_robot_action(robot_id, robot_flag, old_robot_flag, question_type_id):
    """
    :param robot_id: 机器人id
    :param robot_flag: 机器人知识库id
    :param old_robot_flag: 原机器人知识库id
    :param question_type_id: 分类id
    """

    params = add_robot_doc(robot_id, robot_flag, old_robot_flag, question_type_id)
    resp = AddRobotDocApi(**params).send_request()
    return resp


# endregion

# region 初始化访客端
def init_v6_action():
    """
    访客端用户初始化接口
    :param sysNum: 公司ID
    :param source: 来源  0 pc 4 h5
    :param partnerId  用户进线ID
    :param xst: 百度对接id  可以不传
    :param isJs: 默认为 0
    :param ack: 默认为 1
    :param isReComment: 默认为 1
    :param newFlag: 默认为 1
    """
    params = init_v6_data(sysNum=None, source=0, ack=1, isReComment=1, partnerId=None, newFlag=1, xst=None, isJs=0)
    resp = UserInitV6Api(**params).send_request().json()
    return resp


# endregion

# region 获取机器人单轮问题列表
def search_doc_list_action(keyWords=None, robot_flag=None):
    params = search_doc_list_data(keyWords, robot_flag, pageNo=1, keyFlag=1, orderType=1, questionTypeId=None,
                                  typeShowFlag=None,
                                  linkFlag=None,
                                  usedFlag=None, matchFlag=None, create=None, update=None, isDraft=0, createName=None,
                                  createId=None, updateName=None, updateId=None, createTime=None, updateStartDate=None,
                                  updateEndDate=None, updateTime=None, effectStartDate=None, effectEndDate=None,
                                  effectTime=None,
                                  invalidStartDate=None, invalidEndDate=None, invalidTime=None,
                                  robotFlags=None,
                                  pageSize=15,
                                  createStartDate=None, createEndDate=None)
    resp = SearchDocListApi(**params).send_request()
    return resp


# endregion


# region 访客端pc询问新增的单轮问题\多轮问题    source=0
def send_v2_action(base_type, robot_flag, questionTypeId, uid, cid, requestText=None, question=None):
    params = v2_action_data(base_type, robot_flag, questionTypeId, uid, cid, requestText, question)
    v2_actionApi(**params).send_request()


# endregion


# region 新建多轮问题
def add_multi_round_chat_action(questionTypeId, robotFlag):
    # 1.创建流程名称

    real_name = "接口自动化流程" + str(random.randint(0, 100))
    chat_connect = SaveProcessInfoApi(robotFlag, real_name)
    result1 = chat_connect.send_request().json()
    processId = GetKeyword().get_keyword(result1, 'processId')
    # 2.创建分支
    # 获取变量的参数
    chat_connect = QueryVariablesPageByDuolunApi()
    result2 = chat_connect.send_request().json()
    variableId = GetKeyword().get_keyword(result2, 'id')
    variableDataType = GetKeyword().get_keyword(result2, 'variableDataType')
    targets = str([f"{variableId}"])

    chat_connect = CreateNodeApi(processId, nodeName="接口自动节点", remindQuestion="<p>请输入你的姓名:</p>",
                                 nodeType="2", replyError="抱歉没理解您的意思，请您再描述一遍～", replyType="1",
                                 targets=targets, x="452", y="292", template="0", robotFlag=robotFlag)
    result3 = chat_connect.send_request().json()
    nodeId = GetKeyword().get_keyword(result3, 'nodeId')
    # 3.添加分支节点
    condGroupArrStr = [
        {
            "variableId": variableId,
            "variableName": "接口节点1",
            "variableDataType": variableDataType,
            "condValue": "霍勇",  # 获取变量实际的值
            "condGroup": 0,
            "condType": 1,
            "condExp": 1,
            "sort": 0
        }
    ]
    condGroupArrStr = str(condGroupArrStr)
    chat_connect = CreateBranchApi(nodeId, branchName="接口节点2", condGroupArrStr=condGroupArrStr)
    result4 = chat_connect.send_request().json()
    branchId = GetKeyword().get_keyword(result4, 'branchId')
    nodeId = GetKeyword().get_keyword(result4, 'nodeId')
    # 4.连接开始节点与分支
    chat_connect = UpdateFirstNodeApi(nodeId, type="1")
    chat_connect.send_request()
    # 5.添加回复节点
    chat_connect = CreateNodeOneApi(processId, nodeName="接口自动化回复节点", nodeType="3", replyType="1",
                                    nodeValue="<p>接口自动化回复节点的答案</p>", x="143", y="686",
                                    robotFlag=robotFlag)
    result5 = chat_connect.send_request().json()
    nextNodeId = GetKeyword().get_keyword(result5, 'nodeId')

    # 6.连接分支节点与回复节点
    chat_connect = BranchLinkNodeApi(branchId, nextNodeId=nextNodeId, nodeId=nodeId)
    chat_connect.send_request()
    # 7.点击发布
    chat_connect = ReleaseProcessInfoApi(processId, robotId=robotFlag)
    chat_connect.send_request()

    # 8.新建多轮问法
    params = add_robot_doc_multi_data(questionTypeId, processId, robotFlag)
    AddRobotDocInfoApi(**params).send_request()


# endregion

# region 提交机器人满意度评价
def comment_action_action(visitorId, cid):
    params = comment_action_data(visitorId, cid)
    CommentActionApi(**params).send_request()


# endregion

# region  用户结束会话
def out_action(uid):
    params = out_action_data(uid)
    out_actionApi(**params).send_request()


# endregion

# region  删除单轮问题
def delete_robot_action(robot_flag, robotId):
    """
    删除单轮问题
    """
    docIdList = []
    data = search_doc_list_action(keyWords=["autotest"], robot_flag=robot_flag).json()['items']  # 根据关键词查询单轮问题
    for item in data:
        docIdList.append(item['docId'])  # 提取每个 item 中的 docId并放入列表中
    params = delete_doc_data(docIdList, robot_flag, robotId)
    DeleteDocApi(**params).send_request()


# endregion

# region  删除多轮问题
def delete_multi_robot_action(keyWordsArr, usedType, libId, questionTypeId, robotFlag):
    """ 删除多轮问题 """
    resp = get_multi_search_doc_list_action(keyWordsArr, usedType, libId, questionTypeId)
    docIds = " "
    for item in resp:
        docIds = str(item['docId']) + "," + docIds
    params = delete_multi_doc_data(robotFlag, docIds)
    DeleteMultiDocApi(**params).send_request()


# endregion

# region  删除多轮流程问题
def delete_multi_process_action(robotFlag, keyWordsArr=None):
    """ 删除多轮流程问题 """
    resp = get_query_process_list_action(robotFlag, keyWordsArr)
    print(resp)
    processIds = " "
    for item in resp:
        processIds = str(item['processId']) + "," + processIds
    params = delete_multi_process_data(processIds)
    DeleteProcessApi(**params).send_request()
