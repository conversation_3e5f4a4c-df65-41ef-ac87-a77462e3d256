# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function： 会话记录-导出excel

from urllib.parse import urlencode

from api.saas.online_base_saas_api import OnlineBaseSaasApi

'''会话记录-导出excel'''
class ExportChatLogExcelApi(OnlineBaseSaasApi):
    def __init__(self,taskType, exportWay, searchWay, allViewFlag, startDate, endDate, departIdsParam,
                 recordCustomScreen, recordCustomScreenValue, area, urlSearchValue, urlSearchKey, hitKeyword,
                 recordSessionDuration, recordSessionDurationValue, recordSessionAvgDuration,
                 recordSessionAvgDurationValue, recordSessionFirstDuration, recordSessionFirstDurationValue,
                 recordChatScreen, recordChatScreenValue, groupSearchKey, groupSearchValue, staffSearchKey,
                 staffSearchValue, solvedOneTime24H, robotEffectiveness, effectiveness, sessionHumanTransfer,
                 queueStatus, revokeFlag, robotReceptionFlag, offlineType, summaryFlag, questionStatus,
                 invalidSession, operationId, reqType, summaryFieldValue, summaryFieldId, aiClassificationStates,
                 summaryClassifyIds, summaryHandleProgresses, summaryTemplateIds, summaryRemark, fusionSummaryFlag,
                 inviteFlag, isEvaluated, isResovled, resetFlag, scoreFlag, level2Score, score, npsScore,
                 isRobotEvaluated, isRobotResovled, customerFieldsMap, description,exportType):
        super().__init__()
        self.url = self.host + "/chat-statistics/export/task"
        self.method = "post"
        self.headers["Content-Type"] = "application/json"
        self.json = {
            "taskType":taskType,
            "exportWay":exportWay,
            "searchWay":searchWay,
            "allViewFlag":allViewFlag,
            "startDate":startDate,
            "endDate":endDate,
            "departIdsParam":departIdsParam,
            "recordCustomScreen":recordCustomScreen,
            "recordCustomScreenValue":recordCustomScreenValue,
            "area":area,
            "urlSearchValue":urlSearchValue,
            "urlSearchKey":urlSearchKey,
            "hitKeyword":hitKeyword,
            "recordSessionDuration":recordSessionDuration,
            "recordSessionDurationValue":recordSessionDurationValue,
            "recordSessionAvgDuration":recordSessionAvgDuration,
            "recordSessionAvgDurationValue":recordSessionAvgDurationValue,
            "recordSessionFirstDuration":recordSessionFirstDuration,
            "recordSessionFirstDurationValue":recordSessionFirstDurationValue,
            "recordChatScreen":recordChatScreen,
            "recordChatScreenValue":recordChatScreenValue,
            "groupSearchKey":groupSearchKey,
            "groupSearchValue":groupSearchValue,
            "staffSearchKey":staffSearchKey,
            "staffSearchValue":staffSearchValue,
            "solvedOneTime24H":solvedOneTime24H,
            "robotEffectiveness":robotEffectiveness,
            "effectiveness":effectiveness,
            "sessionHumanTransfer":sessionHumanTransfer,
            "queueStatus":queueStatus,
            "revokeFlag":revokeFlag,
            "robotReceptionFlag":robotReceptionFlag,
            "offlineType":offlineType,
            "summaryFlag":summaryFlag,
            "questionStatus":questionStatus,
            "invalidSession":invalidSession,
            "operationId":operationId,
            "reqType":reqType,
            "summaryFieldValue":summaryFieldValue,
            "summaryFieldId":summaryFieldId,
            "aiClassificationStates":aiClassificationStates,
            "summaryClassifyIds":summaryClassifyIds,
            "summaryHandleProgresses":summaryHandleProgresses,
            "summaryTemplateIds":summaryTemplateIds,
            "summaryRemark":summaryRemark,
            "fusionSummaryFlag":fusionSummaryFlag,
            "inviteFlag":inviteFlag,
            "isEvaluated":isEvaluated,
            "isResovled":isResovled,
            "resetFlag":resetFlag,
            "scoreFlag":scoreFlag,
            "level2Score":level2Score,
            "score":score,
            "npsScore":npsScore,
            "isRobotEvaluated":isRobotEvaluated,
            "isRobotResovled":isRobotResovled,
            "customerFieldsMap":customerFieldsMap,
            "description":description,
            "exportType": exportType
        }



