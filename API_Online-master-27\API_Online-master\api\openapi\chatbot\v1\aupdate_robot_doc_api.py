# -*- coding: UTF-8 -*-

from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class UpdateRootDocApi(OnlineBaseOpen_v1_Api):
    """
    修改单轮问题
    """
    def __init__(self, docid, robot_flag, questionid, question_title, match_flag, answerid, answer_desc, question_typeid, used_flag, audit_status, effect_time=None, invalid_time=None):
        super().__init__()
        self.url = self.host + '/api/robot/5/update_robot_doc'
        self.method = 'post'
        self.headers['content-type'] = "application/json"


        self.json = {
            "docid": docid,        # 词条id
            "robot_flag": robot_flag,  # 机器人id
            "questionid": questionid,  # 问题 id
            "question_title": question_title,  # 标准问题
            "match_flag": match_flag,          # 匹配模式：0-智能匹配，1-完全匹配，2-包含匹配
            "answerid": answerid,              # 答案 id
            "answer_desc": answer_desc,        # 知识库配置的答案内容，格式为 HTML
            "question_typeid": question_typeid, # 词条分类 id
            "used_flag": used_flag,             # 词条状态：0-开启，1-停用
            "audit_status":audit_status,        # 有效状态：1-永久有效；2-指定时间有效
            "effect_time": effect_time,         # 生效时间，格式：yyyy-MM-dd HH:mm:ss
            "invalid_time": invalid_time        # 失效时间，格式：yyyy-MM-dd HH:mm:ss
        }