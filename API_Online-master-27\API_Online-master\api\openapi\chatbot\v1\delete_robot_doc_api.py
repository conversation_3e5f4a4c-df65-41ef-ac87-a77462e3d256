# -*- coding: UTF-8 -*-

from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class DeleteRootDocApi(OnlineBaseOpen_v1_Api):
    """
    删除单轮问题
    """
    def __init__(self, docid, robot_flag):
        super().__init__()
        self.url = self.host + '/api/robot/5/delete_robot_doc'
        self.method = 'post'
        self.headers['content-type'] = "application/json"


        self.json = {
            "docid": docid,
            "robot_flag": robot_flag
        }