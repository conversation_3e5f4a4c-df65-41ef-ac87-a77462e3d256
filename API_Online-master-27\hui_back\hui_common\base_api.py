# -*- coding: utf-8 -*-
import base64
from .request_client import RequestsClient

class OnlineBaseSaasApi(RequestsClient):
    """在线客服SAAS API基类"""
    
    configInfo = {}  # 配置信息
    # 保留空的tempId属性以确保兼容性，但实际不再使用
    tempId = ''

    def __init__(self):
        super().__init__()
        # 从配置中获取基础信息
        self.url = self.configInfo.get('url')
        self.loginUser = self.configInfo.get('loginUser')
        self.loginPwd = self.configInfo.get('loginPwd')
        
        # 配置文件中的密码已经是Base64编码，不需要再次编码
        # if self.loginPwd:
        #     self.loginPwd = base64.b64encode(self.loginPwd.encode('utf-8')).decode('utf-8')
        
        # 其他配置信息
        self.loginFlag = self.configInfo.get('loginFlag')
        self.terminalCode = self.configInfo.get('terminalCode')
        self.sysNum = self.configInfo.get('sysNum')
        # 保存已提取的值，用于延迟提取
        self._pending_extractions = []
        self._cached_resp = None
        # 内部变量存储cid
        self._cid = None

    def update_headers(self, new_headers):
        """
        更新请求头
        :param new_headers: 新的请求头字典
        """
        if self.headers is None:
            self.headers = {}
        self.headers.update(new_headers)

    @classmethod
    def set_config(cls, config):
        """
        设置配置信息
        :param config: 配置字典
        """
        cls.configInfo = config
        # 同步配置到变量管理器，确保变量管理器能获取到配置中的值
        from .variables_manager import VariablesManager
        vm = VariablesManager()
        vm.set_config(config)
    
    @property
    def cid(self):
        """
        获取cid属性
        如果请求已完成，返回已提取的cid值
        否则，尝试查找尚未执行的提取请求中是否有cid相关的请求
        """
        # 如果已经设置了cid值，直接返回
        if self._cid is not None:
            return self._cid
            
        # 如果请求尚未完成，检查是否有待执行的cid提取请求
        if self._cached_resp is None and self._pending_extractions:
            for extraction in self._pending_extractions:
                # 检查提取方法是否与cid相关
                method_name = extraction['method']
                args = extraction['args']
                if method_name == 'extract_cid' or (method_name == 'extract_and_save' and args and args[0] == '$.cid' and args[1] == 'cid'):
                    # 如果找到cid相关的提取请求，立即执行请求以获取值
                    self.send_request()
                    # send_request会处理待处理的提取并设置self._cid
                    return self._cid
                    
        # 如果没有找到cid相关的提取请求或请求已完成但未找到cid
        return self._cid
        
    @cid.setter
    def cid(self, value):
        """
        设置cid属性
        """
        self._cid = value
        
    @property
    def response(self):
        """
        获取响应对象的代理
        如果响应尚未准备好，保存提取请求以便稍后执行
        """
        from .request_client import ResponseWrapper
        
        # 创建一个支持延迟执行的代理对象
        class DelayedResponseProxy:
            def __init__(self, api_instance):
                self.api_instance = api_instance
                
            def extract_and_save(self, jsonpath_express, var_name=None, index=0):
                # 记录提取请求，等待请求完成后执行
                self.api_instance._pending_extractions.append({
                    'method': 'extract_and_save',
                    'args': (jsonpath_express, var_name, index)
                })
                # 特殊处理cid提取，如果是提取cid，确保后续send_request会处理
                if jsonpath_express == '$.cid' and var_name == 'cid':
                    self.api_instance.logger.info('延迟提取cid，将在请求完成后处理')
                
                # 始终返回None，表示提取尚未完成
                return None
                
            def extract_cid(self, var_name='cid'):
                # 记录提取请求，等待请求完成后执行
                self.api_instance._pending_extractions.append({
                    'method': 'extract_cid',
                    'args': (var_name,)
                })
                self.api_instance.logger.info('延迟提取cid，将在请求完成后处理')
                # 始终返回None，表示提取尚未完成
                return None
                
            def extract_resp(self, jsonpath_express, index=0, var_name=None):
                # 记录提取请求，等待请求完成后执行
                self.api_instance._pending_extractions.append({
                    'method': 'extract_resp',
                    'args': (jsonpath_express, index, var_name)
                })
                # 始终返回None，表示提取尚未完成
                return None
                
            def extract_value(self, jsonpath_express, index=0, var_name=None):
                # 与extract_resp相同，但提供不同的方法名
                self.api_instance._pending_extractions.append({
                    'method': 'extract_resp',
                    'args': (jsonpath_express, index, var_name)
                })
                # 始终返回None，表示提取尚未完成
                return None
                
            # 确保其他属性访问不会导致错误
            def __getattr__(self, name):
                # 记录方法调用，但实际不执行任何操作
                return lambda *args, **kwargs: None
        
        # 如果已经有缓存的响应，直接返回
        if self._cached_resp is not None:
            return self._cached_resp
        
        # 否则返回代理对象    
        return DelayedResponseProxy(self)
        
    def send_request(self, **kwargs):
        """
        重写send_request方法，在请求完成后执行延迟的提取操作
        """
        # 在发送请求前，自动刷新 data 中的 uid 和 cid 参数
        if hasattr(self, 'data') and self.data is not None:
            # 检查 data 中是否包含 uid 或 cid
            if 'uid' in self.data or 'cid' in self.data:
                # 刷新参数值，确保使用已提取的真实值
                self.refresh_data_with_extracted_params('uid', 'cid')
        
        # 调用父类的send_request方法
        response = super().send_request(**kwargs)
        # 缓存响应对象
        self._cached_resp = response
        
        # 如果有延迟的提取操作，执行它们
        if response and self._pending_extractions:
            for extraction in self._pending_extractions:
                method_name = extraction['method']
                args = extraction['args']
                method = getattr(response, method_name)
                # 执行提取方法
                result = method(*args)
                # 如果是显式提取cid，保存结果
                if method_name in ('extract_cid', 'extract_and_save') and args and args[0] == '$.cid' and args[1] == 'cid':
                    self.cid = result
            
            # 清空延迟队列
            self._pending_extractions = []
        
        return response 

    def validate_and_update(self, **kwargs):
        """
        验证并更新参数值，支持任意参数
        :param kwargs: 要验证和更新的参数，格式为 param_name=value
        :param save_to_config: 是否保存到配置中，默认为True
        :raises ValueError: 当必需参数未设置时抛出异常
        :return: 更新后的参数字典
        """
        # 提取 save_to_config 参数
        save_to_config = kwargs.pop('save_to_config', True)
        
        # 存储更新后的参数
        updated_params = {}
        
        # 处理每个参数
        for param_name, param_value in kwargs.items():
            # 如果传入的值不是None，使用传入的值，否则使用实例属性
            if param_value is not None:
                setattr(self, param_name, param_value)
                updated_params[param_name] = param_value
            else:
                # 尝试从实例属性获取值
                if hasattr(self, param_name):
                    updated_params[param_name] = getattr(self, param_name)
                else:
                    updated_params[param_name] = None
        
        # 验证所有参数是否存在
        missing_params = [name for name, value in updated_params.items() if not value]
        if missing_params:
            raise ValueError(f"以下参数未设置: {', '.join(missing_params)}")
        
        # 如果需要，保存到配置中
        if save_to_config:
            for param_name, param_value in updated_params.items():
                self.configInfo[param_name] = param_value
        
        # 更新data中的参数
        if not hasattr(self, 'data'):
            self.data = {}
        self.data.update(updated_params)
        
        return updated_params

    def validate_and_update_uid_cid(self, uid=None, cid=None, save_to_config=True):
        """
        验证并更新uid和cid（保持向后兼容性）
        :param uid: 用户ID
        :param cid: 客户ID
        :param save_to_config: 是否保存到配置中
        :raises ValueError: 当uid或cid未设置时抛出异常
        :return: (uid, cid) 元组
        """
        result = self.validate_and_update(uid=uid, cid=cid, save_to_config=save_to_config)
        return result.get('uid'), result.get('cid')
    
    def ensure_global_param(self, param_name, jsonpath_expr=None):
        """
        确保全局变量 param_name 已经被提取和可用
        :param param_name: 变量名，如 'cid'
        :param jsonpath_expr: 可选，jsonpath表达式，默认 $.{param_name}
        :return: 变量值
        """
        # 1. 首先从变量管理器获取已提取的值（优先级最高）
        value = self.variables_manager.get_variable(param_name)
        if value and not str(value).startswith('default_'):
            return value

        # 2. 尝试从缓存响应中提取
        if self._cached_resp and hasattr(self._cached_resp, 'extract_and_save'):
            jp = jsonpath_expr or f'$.{param_name}'
            value = self._cached_resp.extract_and_save(jp, param_name)
            if value and not str(value).startswith('default_'):
                return value

        # 3. 对于 uid，不读取配置文件，直接返回变量管理器中的值
        if param_name == 'uid':
            return self.variables_manager.get_variable(param_name)

        # 4. 对于其他参数，尝试从配置获取
        value = self.configInfo.get(param_name)
        if value:
            self.variables_manager.set_variable(param_name, value)
            return value

        # 5. 最后返回全局变量（可能是默认值）
        return self.variables_manager.get_variable(param_name)
    
    def ensure_extracted_params(self, *param_names):
        """
        确保指定的参数使用已提取的真实值，而不是默认值
        这个方法会强制刷新参数值，确保使用最新的提取值
        :param param_names: 参数名列表，如 'uid', 'cid'
        :return: 参数字典
        """
        result = {}
        
        for param_name in param_names:
            # 强制从变量管理器获取最新值
            value = self.variables_manager.get_variable(param_name)
            
            # 如果获取到的是默认值，尝试从其他来源获取
            if not value or str(value).startswith('default_'):
                # 尝试从配置中获取（对于非uid参数）
                if param_name != 'uid':
                    config_value = self.configInfo.get(param_name)
                    if config_value and not str(config_value).startswith('default_'):
                        value = config_value
                        self.variables_manager.set_variable(param_name, value)
            
            result[param_name] = value
            
            # 设置到实例属性
            setattr(self, param_name, value)
        
        return result
    
    def refresh_data_with_extracted_params(self, *param_names):
        """
        刷新 data 中的参数，使用已提取的真实值
        :param param_names: 参数名列表，如 'uid', 'cid'
        """
        if not hasattr(self, 'data') or self.data is None:
            self.data = {}
        
        extracted_params = self.ensure_extracted_params(*param_names)
        
        # 更新 data 中的参数
        for param_name, value in extracted_params.items():
            self.data[param_name] = value
            
        return extracted_params

    def _build_api_url(self, url_key=None, path_key=None, fallback_path='path'):
        """
        构建API URL的通用方法
        :param url_key: URL配置键名，如 'services_summary_url'
        :param path_key: 路径配置键名，如 'services_summary_path'
        :param fallback_path: 回退路径键名，默认 'path'
        :return: 完整的API URL
        """
        env_config = self.configInfo

        # 如果指定了特定的URL键，优先使用
        if url_key and url_key in env_config:
            base_url = env_config[url_key]
            path = env_config.get(path_key, env_config.get(fallback_path, ''))
            return base_url + path

        # 检查是否有user_url配置
        if 'user_url' in env_config and url_key:
            base_url = env_config['user_url']
            path = env_config.get(path_key, env_config.get(fallback_path, ''))
            return base_url + path

        # 检查是否有default_url配置（新增支持）
        if 'default_url' in env_config and path_key:
            base_url = env_config['default_url']
            path = env_config.get(path_key, env_config.get(fallback_path, ''))
            return base_url + path

        # 最后使用基础URL配置
        base_url = env_config.get('url', '')
        path = env_config.get(fallback_path, '')
        return base_url + path

    def _setup_request_config(self, method='post', content_type='application/json'):
        """
        设置请求配置的通用方法
        :param method: HTTP方法
        :param content_type: 内容类型
        """
        self.method = method.lower()
        self.headers = {'content-type': content_type}

        # 根据内容类型决定是否移除json属性
        if 'application/x-www-form-urlencoded' in content_type:
            self.json = None

    def auto_refresh_and_send(self, **kwargs):
        """
        自动刷新必需参数并发送请求的便捷方法
        """
        # 如果子类定义了REQUIRED_PARAMS，自动刷新这些参数
        if hasattr(self, 'REQUIRED_PARAMS') and self.REQUIRED_PARAMS:
            self.refresh_data_with_extracted_params(*self.REQUIRED_PARAMS)

            # 可选：添加调试信息
            if hasattr(self, '__class__'):
                class_name = self.__class__.__name__
                print(f"[{class_name}] 自动刷新参数: {self.REQUIRED_PARAMS}")
                print(f"[{class_name}] 请求数据: {getattr(self, 'data', 'N/A')}")

        # 调用父类的send_request方法
        return super().send_request(**kwargs)