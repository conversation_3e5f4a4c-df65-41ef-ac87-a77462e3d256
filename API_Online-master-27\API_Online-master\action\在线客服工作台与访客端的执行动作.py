# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function：在线客服进线流程前置动作
import os
import sys


ROOT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print("在线客服过来的 ROOT_PATH 的值为：%s" % ROOT_PATH)
sys.path.append(ROOT_PATH)
from api.saas.livechat.Online_Agent import *
from api.saas.livechat.Online_GS_Visitors import *
from api.saas.livechat.Online_WB_WorkBranche import *
from data.Saas.livechat.params_data.satisfaction_params import satisfaction_params
from online_utils.online_cus_utils import *


# 初始化获取访客信息并且转人工阶段状态
def init_visitor_info():
    # 1、获取访客信息
    visitor_rest = GuestInformationConfiguration().send_request().json()
    uid = visitor_rest['uid']
    cid = visitor_rest['cid']
    #  2、转人工
    connect_rest = TransferToHuman(uid=uid, cid=cid).send_request().json()
    return uid, cid, connect_rest

# 查询并清除所有的在线客服
def clear_online_service(flag=0):
    # 查询在线客服
    service_rest = ServiceListToday().send_request().json()
    print(f"查询在线客服结果为：{service_rest}")
    if service_rest['items']:
        for i in service_rest['items']:
            if flag == 1:
                BatchOfflineAdmin(i['staffId']).send_request().json()
                print(f"当前在线客服为：{i['staffId']}，清除完成")
            else:
                if i['staffId'] == ServiceListToday().serviceId:
                    print(f"当前在线客服等于当前登录客服，无需清除")

    else:
        print(f"当前没有在线客服，无需清除")




# 提交有效服务总结
def effective_service_summary(tid, uid, cid):
    #  获取业务列表
    bs_rest = GetUnitInfos(tid).send_request().json()
    #  获取指定业务下的类型列表
    operationId = operationName = reqTypeIdPath = reqTypeNamePath =  ''
    fields =[]
    if bs_rest['items']:
        unit_id = random.choice([(i['unitId'],i['unitName']) for i in bs_rest['items']])
        operationName = unit_id[-1]
        type_list_rest =GetUnifoInfoById(tid, cid, unitId=unit_id[0]).send_request().json()
        if type_list_rest['item']:
            fieldList = type_list_rest['item']['fieldList']
            typeList = type_list_rest['item']['typeList']
            if fieldList:
                filed_info=random.choice(fieldList)
                field_params = {
                    "fieldId": filed_info['fieldId'],
                    "fieldName": filed_info['fieldName'],
                    "fieldValue": fake.text(max_nb_chars=100)
                }
                fields.append(field_params)
            if typeList:
                type_info = random.choice(typeList)
                operationId = type_info['unitId']
                reqTypeIdPath = type_info['typeId']
                reqTypeNamePath = type_info['typeName']
    sub_rest = SummarySubmitVer2(
        tid=tid,
        cid=cid,
        uid=uid,
        questionStatus=1,
        operationId=operationId,
        operationName=operationName,
        reqTypeIdPath=reqTypeIdPath,
        reqTypeNamePath=reqTypeNamePath,
        fields=fields,
        invalidSession=0,
        questionDescribe=fake.text(max_nb_chars=100)
    ).send_request().json()
    print(f"提交有效服务总结结果为：{sub_rest}")

# 提交无效会话服务总结
def invalid_service_summary(tid, uid, cid):
    sub_rest = InvalidSessionSummarySubmitVer2(tid, uid,cid,invalidSession=1).send_request().json()
    print(f"提交无效服务总结结果为：{sub_rest}")
    return sub_rest

def sub_service_summary(tid, uid, cid,invalid_num = random.choice([0,1])):
    if invalid_num == 0:
        effective_service_summary(tid, uid, cid)
    else:
        invalid_service_summary(tid, uid=cid, cid=uid)


# 根据status 判断是否转人工阶段
def is_transfer_human(status,tid,puid, cid, uid,connect_rest=None,chat_num = 1):
    # 决定是否评价
    vr_judge = random.choice(["0", "1"])
    print(f"是否评价：{vr_judge}，1：需要评价，2：不评价")
    # vr_judge = "1"
    if status == 0:
        print(f"访客进入等待队列，直接触发排队留言!!!")
        visitor_leave_message(uid,content="访客排队留言"+fake.text(max_nb_chars=1000))
    if status == 1:
        print(f"进入在线客服工作台。")
        visitor_work_bench_connect(tid, puid, cid, uid, chat_num)
        if vr_judge == "1":
            visitor_branch_evaluate(cid, uid)
            visitor_leave_message(uid)
    if status == 2:
        print(f"没有客服在线，与机器人对话!!!")
        visitor_robot_connect(uid, cid,chat_num)
        visitor_leave_message(uid)
        if vr_judge == "1":
            visitor_robot_evaluate(cid, uid)
    if status == 6:    # 触发分组接待
        if connect_rest['groupList']:
            for group_id in connect_rest['groupList']:
                transfertohuman_rest = TransferToHuman(uid=uid, cid=cid,groupId=group_id.get("groupId")).send_request().json()
                if transfertohuman_rest['status'] == 1:
                    print(f"触发技能组后，转人工成功!")
                    visitor_work_bench_connect(tid, puid, cid, uid, chat_num)
                    if vr_judge == "1":
                        visitor_branch_evaluate(cid, uid)
                        visitor_leave_message(uid)
                elif transfertohuman_rest['status'] == 0:
                    print(f"触发技能组后，访客进入等待队列，直接触发排队留言!!!")
                    visitor_leave_message(uid,groupId=group_id.get("groupId"),content="访客排队留言"+fake.text(max_nb_chars=1000))
        else:
            print(f"没有客服在线，与机器人对话!!!")
            visitor_robot_connect(uid, cid, chat_num)
            if vr_judge == "1":
                visitor_robot_evaluate(cid, uid)
                visitor_leave_message(uid)
    print(f"转人工里面的：uid的结果为：{uid},cid的结果为：{cid}")


# 访客端与工作台进行会话
def visitor_work_bench_connect(tid, puid, cid, uid, chat_num = 5):
    chat_count = 0
    while True:
        # 访客端向工作台发送消息
        content = "发往工作台："+ fake.text(max_nb_chars=100)
        send_msg_to_branch_rest = SendMsgToWorkBench(puid=puid, cid=cid, uid=uid, content=content).send_request().json()
        print(f"访客端向工作台发送消息结果为：\n{send_msg_to_branch_rest}\n")
        # 工作台向访客端发送消息
        content = "发往访客端："+ fake.text(max_nb_chars=100)
        send_msg_visitor_rest = ServiceSendMsg( tid=tid, cid=cid, uid=uid,content=content).send_request().json()
        print(f"工作台向访客端发送消息结果为：\n{send_msg_visitor_rest}\n")
        chat_count += 1
        if chat_count == chat_num:
            break

# 访客端与机器人进行有效会话
def visitor_robot_connect(uid, cid,chat_num=5):
    chat_count = 0
    while True:
        # 访客端向机器人发送消息
        content = "发往工作台："+ fake.text(max_nb_chars=100)
        send_msg_to_robot_rest = SendMsgToRobot(uid, cid,requestText=content).send_request().json()
        print(f"访客端向机器人发送消息结果为：\n{send_msg_to_robot_rest}\n")
        chat_count += 1
        if chat_count == chat_num:
            break

# 访客对机器人进行满意度评价
def visitor_robot_evaluate( cid, uid, score=5, tag = "",remark=None, type=0, commentType="1", scoreFlag=0,
                 scoreExplain=""):
    solved = random.choice(["0", "1"])
    if solved == "0":
        tag = "回答错误,没有解决问题"
        score = 0
    evaluate_rest= VisitorRating(
        cid, uid,score=score, tag=tag, solved=solved, remark=remark,
        type=type, commentType=commentType,
        scoreFlag=scoreFlag,scoreExplain=scoreExplain).send_request().json()
    print(f"访客进行会话评价结果为：\n{evaluate_rest}\n")

# 访客对工作台进行满意度评价
def visitor_branch_evaluate( cid, uid):
    # 获取当前访客端满意度评级模板数据
    satis_temp_rest = GetSatisfactionData(uid=uid).send_request().json()
    # 随机选择分数
    choice_score_info = random.choice(satis_temp_rest['data'])
    # 构建参数
    satis_data = satisfaction_params()
    satis_data['cid'] = cid
    satis_data['uid'] = uid
    satis_data['score'] = choice_score_info['score']
    satis_data['tag'] = choice_score_info['labelName']
    satis_data['scoreFlag'] = choice_score_info['scoreFlag']
    satis_data['scoreExplain'] = choice_score_info['scoreExplain']
    VR_rest = VisitorRating(**satis_data).send_request().json()
    print(f"访客对工作台进行评价结果为：\n\n{VR_rest}\n")

# 访客端留言提交
def visitor_leave_message( uid, groupId=None, content="访客留言"+fake.text(max_nb_chars=100)):
    leave_rest = AllotLeaveMsg(uid, groupId, content).send_request().json()
    print(f"访客留言提交结果为：{leave_rest}\n")

























































































































































































































if __name__ == '__main__':
    init_visitor_info()
































