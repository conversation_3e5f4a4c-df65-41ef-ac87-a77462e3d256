# -*- coding: UTF-8 -*-
from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class SearchDocListApi(OnlineBaseOpen_v1_Api):
    """
    查询知识库词条列表
    """
    def __init__(self,key_flag, question_typeid, robot_flag, page_no, key_words=None, used_flag=None, link_flag=None, answer_flag=None, createid=None, create_start_time=None, create_end_time=None,updateid=None, update_start_date=None, update_end_date=None, effect_start_date=None, effect_end_date=None, invalid_start_date=None, invalid_end_date=None):
        super().__init__()
        self.url = self.host + '/api/robot/5/search_doc_list'
        self.method = 'post'
        self.headers['content-type'] = "application/json"


        self.json = {
            "key_flag": key_flag,            # 1-问题，2-答案   必传
            "question_typeid": question_typeid,    # 词条类型（传-1）  必传
            "robot_flag": robot_flag,          # 知识库所属机器人：0-公共知识库，1-机器人   必传
            "page_no": page_no,             # 当前页码     必传
            "key_words": key_words,            # 搜索关键字   非必传
            "used_flag": used_flag,           # 词条状态，0-启用，1-手动停用，2-系统停用，3-过期停用   非必传
            "link_flag": link_flag,           # 是否有关联问题，0-是，1-否  非必传
            "answer_flag": answer_flag,         # 答案类型:1-文本，2-图片，3-图文，4-视频   非必传
            "createid": createid,             # 词条创建人 id  非必传
            "create_start_time": create_start_time,    # 创建开始时间 非必传
            "create_end_time": create_end_time,      # 创建结束时间  非必传
            "updateid": updateid,             # 词条最后更新人 id   非必传
            "update_start_date": update_start_date,    # 更新开始时间  非必传
            "update_end_date": update_end_date,      # 更新结束时间   非必传
            "effect_start_date": effect_start_date,    # 生效开始时间   非必传
            "effect_end_date": effect_end_date,      # 生效结束时间   非必传
            "invalid_start_date": invalid_start_date,   # 失效开始时间   非必传
            "invalid_end_date": invalid_end_date      # 失效结束时间   非必传
            }