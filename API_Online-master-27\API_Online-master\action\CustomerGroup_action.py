# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function：客群规则相关操作。

from api.saas.livechat.management_center.Online_MC_CustomerGroup import *
from data.Saas.livechat.params_data.CustomerGroup_params import *

# 查询客群规则列表
def query_customer_group_action(ruleTitle=None,pageNo=1,pageSize=15,fieldType=None,fieldValue=None):
    """
    查询客群规则
    :param ruleTitle:客群名称
    :param pageNo:
    :param pageSize:
    :param fieldType:查询类型
    :param fieldValue:
    :return:
    """
    params = query_customer_group_params(ruleTitle,pageNo,pageSize,fieldType,fieldValue)
    return QueryCustomerGroup(**params).send_request().json()
