# -*- coding: UTF-8 -*-
from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class RobotChatApi(OnlineBaseOpen_v1_Api):
    """
    用户咨询机器人
    """
    def __init__(self, partnerid, question, user_nick='hha', source='0', robot_flag=None):
        super().__init__()
        self.url = self.host + '/api/chat/5/user/robot_chat'
        self.method = 'post'
        self.json = {
            "partnerid": partnerid,  # 用户id
            "question": question,     # 用户问题
            "user_nick": user_nick,   # 用户昵称
            "source": source,         # 用户渠道
            "robot_flag": robot_flag  # 机器人编号
            }