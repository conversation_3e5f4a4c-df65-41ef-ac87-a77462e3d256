# -*- coding: UTF-8 -*

from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class RobotGuideApi(OnlineBaseOpen_v1_Api):
    """
    获取常见问题
    """
    def __init__(self, id, faqid=None):
        super().__init__()
        self.url = self.host + '/api/robot/5/get_robot_guide'
        self.method = 'post'
        self.headers['content-type'] = "application/json"

        self.json = {
            'robot_flag': id,   # 机器人id
            'faqid': faqid      # 分组id
        }