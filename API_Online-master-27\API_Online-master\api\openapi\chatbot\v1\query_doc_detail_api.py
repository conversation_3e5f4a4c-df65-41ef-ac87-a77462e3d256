# -*- coding: UTF-8 -*-
from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class QueryDocDetailApi(OnlineBaseOpen_v1_Api):
    """
    查询知识库词条
    """
    def __init__(self, docid, robot_flag):
        super().__init__()
        self.url = self.host + '/api/robot/5/query_doc_detail'
        self.method = 'post'
        self.headers['content-type'] = "application/json"


        self.json = {
            "docid": docid,
            "robot_flag": robot_flag
            }