# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function： 留言记录-导出

from urllib.parse import urlencode

from api.saas.online_base_saas_api import OnlineBaseSaasApi

'''留言记录-未处理留言导出'''
class ExportMessageLogApi(OnlineBaseSaasApi):
    def __init__(self,taskType,status,leaveMsgStartTime,leaveMsgEndTime,uname,tel,leaveMsgId,leaveMsgContent,
                 leaveMsgContentReverseFiltration,allotStatus,cusGroupIds,allotStartTime,allotEndTime,dealStartTime,
                 dealEndTime,vipType,sources,leaveMsgReason,allotAdminIds,currentAdminIds,dealAdminIds,sortKey,sortValue,
                 type, staffId,leaveMsgExportType,endDate,description,startDate):
        super().__init__()
        self.url = self.host + "/chat-statistics/export/task"
        self.method = "post"
        self.headers["Content-Type"] = "application/json"
        self.json = {
            "taskType":taskType,
            "status":status,
            "leaveMsgStartTime":leaveMsgStartTime,
            "leaveMsgEndTime":leaveMsgEndTime,
            "uname":uname,
            "tel":tel,
            "leaveMsgId":leaveMsgId,
            "leaveMsgContent":leaveMsgContent,
            "leaveMsgContentReverseFiltration":leaveMsgContentReverseFiltration,
            "allotStatus":allotStatus,
            "cusGroupIds":cusGroupIds,
            "allotStartTime":allotStartTime,
            "allotEndTime":allotEndTime,
            "dealStartTime":dealStartTime,
            "dealEndTime":dealEndTime,
            "vipType":vipType,
            "sources":sources,
            "leaveMsgReason":leaveMsgReason,
            "allotAdminIds":allotAdminIds,
            "currentAdminIds":currentAdminIds,
            "dealAdminIds":dealAdminIds,
            "sortKey":sortKey,
            "sortValue":sortValue,
            "type":type,
            "description":description,
            "staffId":staffId,
            "leaveMsgExportType":leaveMsgExportType,
            "startDate":startDate,
            "endDate":endDate
        }

