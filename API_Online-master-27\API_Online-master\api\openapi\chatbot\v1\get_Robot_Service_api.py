# -*- coding: UTF-8 -*-

from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class GetRobotServiceApi(OnlineBaseOpen_v1_Api):
    """
    查询公司角色人员信息
    """
    def __init__(self, page, size):
        super().__init__()
        self.url = "https://imm.sobot.com/chat-statistics/condition/getRobotService"
        self.method = 'post'
        self.headers['content-type'] = "application/json"
        self.json = {
            'page': page,
            'size': size
        }