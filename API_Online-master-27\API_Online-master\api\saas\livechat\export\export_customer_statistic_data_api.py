# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function： 客户统计-导出数据

from urllib.parse import urlencode

from api.saas.online_base_saas_api import OnlineBaseSaasApi

'''客户统计-导出数据'''
class ExportCustomerStatisticApi(OnlineBaseSaasApi):
    def __init__(self,taskType,allViewFlag,departIdsParam,groupIdsParam,staffIdsParam,robotIdsParam,source,description,searchWay,startDate,endDate):
        super().__init__()
        self.url = self.host + "/chat-statistics/export/task"
        self.method = "post"
        self.headers["Content-Type"] = "application/json"
        self.json = {
            "taskType":taskType,
            "searchWay":searchWay,
            "allViewFlag":allViewFlag,
            "departIdsParam":departIdsParam,
            "groupIdsParam":groupIdsParam,
            "staffIdsParam":staffIdsParam,
            "robotIdsParam":robotIdsParam,
            "startDate":startDate,
            "endDate":endDate,
            "source":source,
            "description":description
        }

