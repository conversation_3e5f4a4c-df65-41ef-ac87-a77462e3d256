# !/usr/bin python3                                 
# encoding: utf-8 -*-
# @Function：
import random

from api.saas.livechat.management_center.Online_MC_CustomerGroup import *
from api.saas.livechat.Online_setting_workbranch_SCI_getallcusFields_api import GetCustomFieldInfoList



# 触发条件--渠道
def source_data_params(source_code="0",simple_size=1,ruleRel=0):
    """
    @param source_code:
    @param simple_size: 样本数量。默认为1
    @param ruleRel:
    @return:
    """
    ruleDetailList = []
    try:
        if source_code not in ["0","2","4"]:
            print("source_code参数错误，请输入0、2、4")
            fieldList = [{
                "fieldName": "source",
                "fieldValue": "rule_source_all"
            }]
        else:
            rest1 = QueryChildSource(source=source_code).send_request().json()
            if rest1['items']:
                channel_id_list = []
                for i in rest1['items']:
                    channel_id_list.append(i['channelFlag'])
                if simple_size > len(channel_id_list):
                    print("simple_size参数错误，请输入小于等于子渠道个数的数字")
                    random_ch_rest_list = random.sample(channel_id_list, k=1)
                else:
                    random_ch_rest_list = random.sample(channel_id_list,k =simple_size)
                fieldValue = ','.join(map(str, random_ch_rest_list))
                fieldList = [{
                            "fieldName": "source",
                            "fieldValue": source_code },
                        { "fieldName": "channelFlag",
                            "fieldValue": fieldValue}]
            else:
                fieldList = [{
                            "fieldName": "source",
                            "fieldValue": "rule_source_all"
                        }]
        source_params_dic = {
            "ruleType": 0,
            "ruleOptType": 8,
            "ruleRel": ruleRel,
            "fieldList":fieldList
        }
        ruleDetailList.append(source_params_dic)
        return ruleDetailList
    except Exception as e:
        print("获取渠道数据失败，直接返回全部渠道",e)


# 触发条件--ip
def ip_data_params(ruleRel=0):
    # 获取国家id
    ruleDetailList=[]

    rest1 = GetCountryProvinceCity().send_request().json()
    if rest1.get('items')[0]:
        country_info_list = rest1['items'][0]
        random_country_info = random.choice(country_info_list)
        countryId   =   random_country_info['countryId']
        random_province_info =random.choice(random_country_info['provinces'])
        if random_province_info:
            cities_info_list = random_province_info['cities']
            if len(cities_info_list) == 0:
                print("没有找到城市数据")
                fieldList = [ {
                            "fieldName": "ip",
                            "fieldValue": rf"{countryId}/{random_province_info['provinceId']}"
                        } ]
            else:
                random_city_info = random.choice(cities_info_list)
                fieldList = [ {
                            "fieldName": "ip",
                            "fieldValue":  rf"{countryId}/{random_city_info['provinceId']}/{random_city_info['cityId']}"
                        } ]
        else:
            fieldList = [ {
                        "fieldName": "ip",
                        "fieldValue": f"{countryId}"
                    } ]
    else:
        print("获取国家id失败，默认返回全部ip")
        fieldList = [ {
                    "fieldName": "ip",
                    "fieldValue": "rule_ip_all"
                } ]
    ip_params_dic = {
        "ruleType": 4,
        "ruleOptType": 8,
        "ruleRel": ruleRel,
        "fieldList": fieldList
    }
    ruleDetailList.append(ip_params_dic)
    return ruleDetailList

# 触发条件--自定义字段
def custom_field_data_params(fieldId=None,ruleRel=0):
    rest = GetCustomFieldInfoList().send_request().json()
    if rest.get('items'):
        field_info_dic = random.choice(rest['items'])
        fieldName = field_info_dic['fieldId']
        cus_params_dic = {
            "ruleType": 5,
            "ruleOptType": 0,
            "ruleRel": ruleRel,
            "ruleContent": "exFields",
            "fieldList": [
                {
                    "fieldName":fieldName ,
                    "fieldValue":1
                }
            ]
        }
    return cus_params_dic


if __name__ == '__main__':
    pass